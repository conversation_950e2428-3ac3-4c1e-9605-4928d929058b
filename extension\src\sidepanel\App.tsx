import React from 'react'
import type { AccountsState, Conversation, ChatMessage } from '../types'
import { listConversations as apiListConversations, listMessages as apiListMessages, chatAsk } from '../lib/chat'
import { InnerSidebar } from '../components/InnerSidebar'
import { ChatHistoryModal } from '../components/ChatHistoryModal'
import { Send, Loader2, MessageSquare } from 'lucide-react'

export default function App() {
  const [lastPing, setLastPing] = React.useState<number | null>(null)
  const [accounts, setAccounts] = React.useState<AccountsState>({ accounts: [] })
  // Phase 5 state
  const [conversations, setConversations] = React.useState<Conversation[]>([])
  const [activeConversationId, setActiveConversationId] = React.useState<string | null>(null)
  const [messages, setMessages] = React.useState<ChatMessage[]>([])
  const [input, setInput] = React.useState('')
  const [isStreaming, setIsStreaming] = React.useState(false)
  const [streamBuffer, setStreamBuffer] = React.useState('')
  const [showChatHistory, setShowChatHistory] = React.useState(false)
  const [isNewChatMode, setIsNewChatMode] = React.useState(false)

  const pingBackground = () => {
    chrome.runtime.sendMessage({ type: 'PING' }, (resp) => {
      if (chrome.runtime.lastError) {
        console.warn('PING error', chrome.runtime.lastError)
        return
      }
      setLastPing(resp?.ts ?? null)
    })
  }

  const refreshAccounts = React.useCallback(() => {
    chrome.runtime.sendMessage({ type: 'accounts/list' }, (resp: AccountsState) => {
      if (chrome.runtime.lastError) {
        console.warn('accounts/list error', chrome.runtime.lastError)
        return
      }
      setAccounts(resp)
    })
  }, [])

  const addAccount = async () => {
    try {
      await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({ type: 'accounts/add' }, (resp) => {
          if (chrome.runtime.lastError) return reject(chrome.runtime.lastError)
          if (resp?.error) return reject(new Error(resp.error))
          resolve(null)
        })
      })
      refreshAccounts()
    } catch (e) {
      alert(`Add account failed: ${(e as any)?.message || e}`)
    }
  }

  const switchAccount = async (accountId: string) => {
    await new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'accounts/switch', accountId }, () => resolve(null))
    })
    refreshAccounts()
  }

  const signoutAccount = async (accountId: string) => {
    await new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'accounts/signout', accountId }, () => resolve(null))
    })
    refreshAccounts()
  }

  React.useEffect(() => {
    refreshAccounts()
  }, [refreshAccounts])

  // Load conversations on mount
  const refreshConversations = React.useCallback(async () => {
    try {
      const list = await apiListConversations(50)
      setConversations(list)
      // Only auto-select first conversation if we're not in new chat mode and no active conversation
      if (!activeConversationId && !isNewChatMode && list[0]?.id) {
        setActiveConversationId(list[0].id)
      }
    } catch (e) {
      console.warn('load conversations failed', e)
    }
  }, [activeConversationId, isNewChatMode])

  React.useEffect(() => {
    refreshConversations()
  }, [refreshConversations])

  // Load messages when active conversation changes
  const refreshMessages = React.useCallback(async (conversationId: string | null) => {
    if (!conversationId) return setMessages([])
    try {
      const rows = await apiListMessages(conversationId, 500)
      setMessages(rows)
    } catch (e) {
      console.warn('load messages failed', e)
    }
  }, [])

  React.useEffect(() => {
    refreshMessages(activeConversationId)
  }, [activeConversationId, refreshMessages])

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + H to open chat history
      if ((e.ctrlKey || e.metaKey) && e.key === 'h' && !e.shiftKey) {
        e.preventDefault()
        setShowChatHistory(true)
      }
      // Ctrl/Cmd + N for new chat
      if ((e.ctrlKey || e.metaKey) && e.key === 'n' && !e.shiftKey) {
        e.preventDefault()
        startNewChat()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const onSubmit = async (ev: React.FormEvent) => {
    ev.preventDefault()
    if (!input.trim() || isStreaming) return
    setIsStreaming(true)
    setStreamBuffer('')
    try {
      // Try to retrieve active Gmail access token from background
      const gmailAccessToken: string | null = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'tokens/getActive' }, (resp) => {
          if (chrome.runtime.lastError) return resolve(null)
          const at = (resp && resp.access_token) ? String(resp.access_token) : null
          resolve(at)
        })
      })
      await chatAsk(
        { conversationId: activeConversationId || undefined, message: input.trim(), title: conversations.length === 0 ? 'New chat' : null, accountId: accounts.activeAccountId || null, gmailAccessToken },
        (evt) => {
          if (evt.type === 'meta') {
            if (!activeConversationId) {
              setActiveConversationId(evt.conversationId)
              setIsNewChatMode(false) // Exit new chat mode when conversation is created
              // also refresh list soon to include new convo on top
              setTimeout(() => refreshConversations(), 100)
            }
          } else if (evt.type === 'delta') {
            setStreamBuffer((prev) => prev + evt.text)
          } else if (evt.type === 'error') {
            alert(`Chat error: ${evt.message}`)
          } else if (evt.type === 'done') {
            setIsStreaming(false)
            setInput('')
            setStreamBuffer('')
            // refresh messages and conversations
            refreshMessages(activeConversationId)
            refreshConversations()
          }
        }
      )
    } catch (e) {
      alert(`Chat failed: ${(e as any)?.message || e}`)
      setIsStreaming(false)
    }
  }

  const retryLast = () => {
    const lastUser = [...messages].reverse().find((m) => m.role === 'user')
    if (lastUser?.content) {
      setInput(lastUser.content)
    }
  }

  const copyLastAssistant = async () => {
    const lastAssistant = [...messages].reverse().find((m) => m.role === 'assistant')
    const text = (lastAssistant?.content || '') + (isStreaming ? streamBuffer : '')
    if (text) await navigator.clipboard.writeText(text)
  }

  const startNewChat = () => {
    setActiveConversationId(null)
    setMessages([])
    setInput('')
    setStreamBuffer('')
    setIsNewChatMode(true)
  }

  const selectConversation = (conversationId: string) => {
    if (conversationId !== activeConversationId) {
      setActiveConversationId(conversationId)
      // Messages will be loaded by useEffect
    }
  }

  return (
    <div className="h-screen w-full flex flex-col bg-white text-gray-900">
      <main className="flex-1 flex min-h-0">
        {/* Main Chat Area */}
        <section className="flex flex-col min-w-0 flex-1">
          {/* Messages */}
          <div className="flex-1 overflow-auto p-4 space-y-4">
            {messages.map((m) => (
              <div key={m.id} className="text-sm">
                <div className="text-[10px] text-gray-500 mb-1">
                  {m.role.toUpperCase()} {m.tool_name ? `• ${m.tool_name}` : ''}
                </div>
                {m.content && <div className="whitespace-pre-wrap leading-relaxed">{m.content}</div>}
                {!m.content && m.result_excerpt && (
                  <pre className="text-[11px] bg-gray-50 border rounded p-2 overflow-auto max-h-40">{m.result_excerpt}</pre>
                )}
              </div>
            ))}
            {isStreaming && (
              <div className="text-sm">
                <div className="text-[10px] text-gray-500 mb-1">ASSISTANT • streaming</div>
                <div className="whitespace-pre-wrap leading-relaxed">{streamBuffer || '…'}</div>
              </div>
            )}
            {!isStreaming && messages.length === 0 && (
              <div className="text-center text-gray-500 mt-8">
                <div className="text-lg mb-2">👋</div>
                <div className="text-sm">Welcome to AI Email Agent</div>
                <div className="text-xs mt-1">Start by asking something like "Summarize my inbox today" or use the quick actions on the right.</div>
              </div>
            )}
          </div>

          {/* Chat History Icon */}
          <div className="px-4 pb-2 flex justify-start">
            <button
              onClick={() => setShowChatHistory(true)}
              className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors group relative"
              title="Chat history (Ctrl+H)"
            >
              <MessageSquare size={16} />
              <span>Chat history</span>
              {conversations.length > 0 && (
                <span className="text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full">
                  {conversations.length}
                </span>
              )}
              {/* Keyboard shortcut hint */}
              <span className="text-xs text-gray-400 ml-auto opacity-0 group-hover:opacity-100 transition-opacity">
                Ctrl+H
              </span>
            </button>
          </div>

          {/* Floating Composer */}
          <div className="p-4 pt-0">
            <form onSubmit={onSubmit} className="relative">
              <textarea
                className="w-full text-sm bg-gray-50 border-0 rounded-2xl p-4 pr-12 resize-none h-14 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white shadow-sm transition-all"
                placeholder="Ask anything about your inbox…"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    if (input.trim() && !isStreaming) {
                      onSubmit(e)
                    }
                  }
                }}
                disabled={isStreaming}
              />
              {isStreaming && (
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <Loader2 size={16} className="animate-spin text-blue-500" />
                </div>
              )}
            </form>
          </div>
        </section>

        {/* Right Inner Sidebar */}
        <InnerSidebar
          onQuickAction={(action) => setInput(action)}
          onRetry={retryLast}
          onCopy={copyLastAssistant}
          accounts={accounts}
          onAddAccount={addAccount}
          onSwitchAccount={switchAccount}
          onSignoutAccount={signoutAccount}
        />
      </main>

      {/* Chat History Modal */}
      <ChatHistoryModal
        open={showChatHistory}
        onOpenChange={setShowChatHistory}
        conversations={conversations}
        activeConversationId={activeConversationId}
        onSelectConversation={selectConversation}
        onNewChat={startNewChat}
      />
    </div>
  )
}
