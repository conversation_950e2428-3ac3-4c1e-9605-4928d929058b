import React from 'react'
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription } from './ui/dialog'
import { Button } from './ui/button'
import { AlertTriangle, Shield, Mail, Edit, Send } from 'lucide-react'
import { requestScopesForOperation } from '../lib/config'

interface ScopeRequestDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  operation: 'modify' | 'compose' | 'send' | 'full'
  onSuccess: (accessToken: string) => void
  onError: (error: string) => void
}

const OPERATION_INFO = {
  modify: {
    title: '<PERSON><PERSON>n quyền chỉnh sửa Gmail',
    description: '<PERSON><PERSON> thực hiện hành động này, <PERSON>ng dụng cần quyền chỉnh sửa email (đánh dấu đã đọc, thêm nhãn, xóa, v.v.)',
    icon: Edit,
    permissions: [
      'Xem và chỉnh sửa nhãn email',
      '<PERSON><PERSON><PERSON> dấu email đã đọc/ch<PERSON>a đọc',
      '<PERSON><PERSON><PERSON> trữ và xóa email',
      '<PERSON>u<PERSON><PERSON> lý thư mục email'
    ]
  },
  compose: {
    title: '<PERSON><PERSON><PERSON> quyền soạn thảo Gmail',
    description: 'Để tạo và chỉnh sửa email nháp, ứng dụng cần quyền soạn thảo email.',
    icon: Mail,
    permissions: [
      'Tạo email nháp mới',
      'Chỉnh sửa email nháp',
      'Quản lý email nháp',
      'Tất cả quyền chỉnh sửa'
    ]
  },
  send: {
    title: 'Cần quyền gửi Gmail',
    description: 'Để gửi email thay mặt bạn, ứng dụng cần quyền gửi email.',
    icon: Send,
    permissions: [
      'Gửi email thay mặt bạn',
      'Gửi email nháp',
      'Gửi email mới',
      'Tất cả quyền soạn thảo và chỉnh sửa'
    ]
  },
  full: {
    title: 'Cần quyền đầy đủ Gmail',
    description: 'Để sử dụng tất cả tính năng của AI Email Agent, ứng dụng cần quyền truy cập đầy đủ Gmail.',
    icon: Shield,
    permissions: [
      'Đọc tất cả email',
      'Chỉnh sửa và quản lý email',
      'Tạo và gửi email',
      'Quản lý nhãn và thư mục'
    ]
  }
}

export function ScopeRequestDialog({
  open,
  onOpenChange,
  operation,
  onSuccess,
  onError
}: ScopeRequestDialogProps) {
  const [isRequesting, setIsRequesting] = React.useState(false)
  const info = OPERATION_INFO[operation]
  const Icon = info.icon

  const handleRequest = async () => {
    setIsRequesting(true)
    try {
      const accessToken = await requestScopesForOperation(operation)
      onSuccess(accessToken)
      onOpenChange(false)
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to request permissions')
    } finally {
      setIsRequesting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Icon size={24} className="text-blue-600" />
            </div>
            <DialogTitle className="text-lg">{info.title}</DialogTitle>
          </div>
          <DialogDescription className="text-sm text-gray-600">
            {info.description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle size={16} className="text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <p className="font-medium mb-1">Quyền bổ sung cần thiết</p>
                <p>Bạn sẽ được chuyển đến trang xác thực Google để cấp thêm quyền.</p>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Quyền sẽ được yêu cầu:</h4>
            <ul className="space-y-1">
              {info.permissions.map((permission, index) => (
                <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0" />
                  {permission}
                </li>
              ))}
            </ul>
          </div>

          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isRequesting}
              className="flex-1"
            >
              Hủy
            </Button>
            <Button
              onClick={handleRequest}
              disabled={isRequesting}
              className="flex-1"
            >
              {isRequesting ? 'Đang xử lý...' : 'Cấp quyền'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
