import React from 'react'
import { But<PERSON> } from './ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from './ui/dropdown-menu'
import { 
  Mail, 
  PenTool, 
  Tag, 
  Clock, 
  Search, 
  Edit3, 
  RotateCcw, 
  Copy, 
  User,
  UserPlus,
  LogOut,
  RefreshCw
} from 'lucide-react'
import type { AccountsState } from '../types'

interface InnerSidebarProps {
  onQuickAction: (action: string) => void
  onRetry: () => void
  onCopy: () => void
  accounts: AccountsState
  onAddAccount: () => void
  onSwitchAccount: (accountId: string) => void
  onSignoutAccount: (accountId: string) => void
  onRequestFullScopes?: () => void
}

export function InnerSidebar({ onQuickAction, onRetry, onCopy, accounts, onAddAccount, onSwitchAccount, onSignoutAccount, onRequestFullScopes }: InnerSidebarProps) {
  const quickActions = [
    {
      id: 'summarize-inbox',
      icon: Mail,
      label: 'Summarize Inbox',
      action: 'Summarize my inbox today with key action items.'
    },
    {
      id: 'draft-reply',
      icon: PenTool,
      label: 'Draft Reply',
      action: 'Draft a polite reply acknowledging receipt and proposing a call tomorrow morning.'
    },
    {
      id: 'label-suggestions',
      icon: Tag,
      label: 'Label Suggestions',
      action: 'Suggest labels for the recent important emails and explain the rationale.'
    },
    {
      id: 'schedule-email',
      icon: Clock,
      label: 'Schedule Email',
      action: 'Help me schedule an email to be sent later.'
    },
    {
      id: 'search-emails',
      icon: Search,
      label: 'Search Emails',
      action: 'Search through my emails for specific content or sender.'
    },
    {
      id: 'compose-email',
      icon: Edit3,
      label: 'Compose Email',
      action: 'Help me compose a new email.'
    }
  ]

  return (
    <div className="w-11 bg-gray-50 flex flex-col items-center py-2 gap-2 flex-shrink-0">
      {/* Quick Actions */}
      <div className="flex flex-col gap-1">
        {quickActions.map((action) => {
          const IconComponent = action.icon
          return (
            <button
              key={action.id}
              onClick={() => onQuickAction(action.action)}
              className="w-9 h-9 rounded-md bg-white border hover:bg-gray-100 flex items-center justify-center transition-colors group relative"
              title={action.label}
            >
              <IconComponent size={16} className="text-gray-600" />
              {/* Tooltip */}
              <div className="absolute right-full mr-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-10">
                {action.label}
              </div>
            </button>
          )
        })}
      </div>

      {/* Divider */}
      <div className="w-6 h-px bg-gray-300 my-1"></div>

      {/* Utility Actions */}
      <div className="flex flex-col gap-1">
        <button
          onClick={onRetry}
          className="w-9 h-9 rounded-md bg-white border hover:bg-gray-100 flex items-center justify-center transition-colors group relative"
          title="Retry Last Message"
        >
          <RotateCcw size={16} className="text-gray-600" />
          <div className="absolute right-full mr-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-10">
            Retry Last
          </div>
        </button>

        <button
          onClick={onCopy}
          className="w-9 h-9 rounded-md bg-white border hover:bg-gray-100 flex items-center justify-center transition-colors group relative"
          title="Copy Last Response"
        >
          <Copy size={16} className="text-gray-600" />
          <div className="absolute right-full mr-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-10">
            Copy Response
          </div>
        </button>
      </div>

      {/* Account Dropdown */}
      <div className="mt-auto">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="w-9 h-9 rounded-md bg-white border hover:bg-gray-100 flex items-center justify-center transition-colors group relative">
              {(() => {
                const active = accounts.accounts.find((a) => a.id === accounts.activeAccountId)
                return active?.picture ? (
                  <img src={active.picture} className="w-6 h-6 rounded-full object-cover" alt="avatar" />
                ) : (
                  <User size={16} className="text-gray-600" />
                )
              })()}
              <div className="absolute right-full mr-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-10">
                Account Menu
              </div>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem onClick={onAddAccount} className="flex items-center gap-2">
              <UserPlus size={16} />
              Add Account
            </DropdownMenuItem>

            {onRequestFullScopes && (
              <DropdownMenuItem onClick={onRequestFullScopes} className="flex items-center gap-2 text-blue-600">
                <RefreshCw size={16} />
                Request Full Permissions
              </DropdownMenuItem>
            )}
            
            {accounts.accounts.length > 0 && (
              <>
                <DropdownMenuSeparator />
                {accounts.accounts.map((account) => {
                  const isActive = account.id === accounts.activeAccountId
                  return (
                    <div key={account.id}>
                      <DropdownMenuItem 
                        onClick={() => !isActive && onSwitchAccount(account.id)}
                        className={`flex items-center justify-between ${isActive ? 'bg-gray-100' : ''}`}
                      >
                        <div className="flex items-center gap-2">
                          {account.picture ? (
                            <img src={account.picture} className="w-4 h-4 rounded-full object-cover" alt="avatar" />
                          ) : (
                            <User size={16} />
                          )}
                          <div className="flex flex-col">
                            <span className="text-sm truncate max-w-32">{account.email}</span>
                            {isActive && <span className="text-xs text-green-600">Active</span>}
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {!isActive && (
                            <button 
                              onClick={(e) => {
                                e.stopPropagation()
                                onSwitchAccount(account.id)
                              }}
                              className="text-xs px-1 py-0.5 rounded border hover:bg-gray-50"
                            >
                              <RefreshCw size={12} />
                            </button>
                          )}
                          <button 
                            onClick={(e) => {
                              e.stopPropagation()
                              onSignoutAccount(account.id)
                            }}
                            className="text-xs px-1 py-0.5 rounded border hover:bg-gray-50 text-red-600"
                          >
                            <LogOut size={12} />
                          </button>
                        </div>
                      </DropdownMenuItem>
                    </div>
                  )
                })}
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
