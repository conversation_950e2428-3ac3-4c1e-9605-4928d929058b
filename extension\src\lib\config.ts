export const BACKEND_BASE_URL = (import.meta as any).env?.VITE_BACKEND_BASE_URL || 'https://ai-email-agent-worker.vudinhhiep3004.workers.dev'

export const BASE_SCOPES = [
  'openid',
  'email',
  'profile',
  'https://www.googleapis.com/auth/userinfo.email',
]

// For Phase 4.0 (read-only Gmail tools), include gmail.readonly
export const DEFAULT_SCOPES = [
  ...BASE_SCOPES,
  'https://www.googleapis.com/auth/gmail.readonly',
]

// Additional scopes for different operations
export const MODIFY_SCOPES = [
  ...DEFAULT_SCOPES,
  'https://www.googleapis.com/auth/gmail.modify',
]

export const COMPOSE_SCOPES = [
  ...MODIFY_SCOPES,
  'https://www.googleapis.com/auth/gmail.compose',
]

export const SEND_SCOPES = [
  ...COMPOSE_SCOPES,
  'https://www.googleapis.com/auth/gmail.send',
]

// Full scopes for complete functionality
export const FULL_SCOPES = [
  ...BASE_SCOPES,
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.modify',
  'https://www.googleapis.com/auth/gmail.compose',
  'https://www.googleapis.com/auth/gmail.send',
]

export const OAUTH_PROMPT = 'consent'
export const OAUTH_ACCESS_TYPE = 'offline'
export const OAUTH_INCLUDE_GRANTED_SCOPES = 'true'

// Helper function to request additional scopes
export async function requestScopesForOperation(operation: 'modify' | 'compose' | 'send' | 'full'): Promise<string> {
  let scopes: string[]

  switch (operation) {
    case 'modify':
      scopes = MODIFY_SCOPES
      break
    case 'compose':
      scopes = COMPOSE_SCOPES
      break
    case 'send':
      scopes = SEND_SCOPES
      break
    case 'full':
      scopes = FULL_SCOPES
      break
    default:
      throw new Error(`Unknown operation: ${operation}`)
  }

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(
      { type: 'accounts/requestScopes', scopes },
      (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError)
        } else if (response?.error) {
          reject(new Error(response.error))
        } else {
          resolve(response.access_token)
        }
      }
    )
  })
}
